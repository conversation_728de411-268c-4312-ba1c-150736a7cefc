import React from 'react';
import { Helmet } from 'react-helmet-async';
import { useSEOContent } from '../hooks/useCMSContent';

interface SEOHeadProps {
  page: string;
  title?: string;
  description?: string;
  keywords?: string;
  ogImage?: string;
  canonicalUrl?: string;
}

const SEOHead: React.FC<SEOHeadProps> = ({
  page,
  title,
  description,
  keywords,
  ogImage,
  canonicalUrl
}) => {
  const { seoContent, loading } = useSEOContent();

  // Use provided props or fallback to CMS content
  const pageData = seoContent?.[page] || {};
  const finalTitle = title || pageData.title || 'Darvi Group - Agricultural Solutions';
  const finalDescription = description || pageData.description || 'Comprehensive agricultural solutions and farmer registration platform.';
  const finalKeywords = keywords || pageData.keywords || 'agriculture, farming, consultation';
  const finalOgImage = ogImage || pageData.ogImage || '/darvi-logo.png';
  const finalCanonicalUrl = canonicalUrl || `https://darvigroup.in${page === 'home' ? '' : `/${page}`}`;

  if (loading) {
    return null;
  }

  return (
    <Helmet>
      <title>{finalTitle}</title>
      <meta name="description" content={finalDescription} />
      <meta name="keywords" content={finalKeywords} />
      
      {/* Open Graph / Facebook */}
      <meta property="og:type" content="website" />
      <meta property="og:url" content={finalCanonicalUrl} />
      <meta property="og:title" content={finalTitle} />
      <meta property="og:description" content={finalDescription} />
      <meta property="og:image" content={finalOgImage} />
      
      {/* Twitter */}
      <meta property="twitter:card" content="summary_large_image" />
      <meta property="twitter:url" content={finalCanonicalUrl} />
      <meta property="twitter:title" content={finalTitle} />
      <meta property="twitter:description" content={finalDescription} />
      <meta property="twitter:image" content={finalOgImage} />
      
      {/* Canonical URL */}
      <link rel="canonical" href={finalCanonicalUrl} />
      
      {/* Additional meta tags */}
      <meta name="robots" content="index, follow" />
      <meta name="author" content="Darvi Group" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      
      {/* Structured data for agriculture business */}
      <script type="application/ld+json">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": "Organization",
          "name": "Darvi Group",
          "url": "https://darvigroup.in",
          "logo": "https://darvigroup.in/darvi-logo.png",
          "description": finalDescription,
          "contactPoint": {
            "@type": "ContactPoint",
            "telephone": "+91-XXXXXXXXXX",
            "contactType": "customer service"
          },
          "sameAs": [
            "https://www.facebook.com/darvigroup",
            "https://www.instagram.com/darvigroup"
          ]
        })}
      </script>
    </Helmet>
  );
};

export default SEOHead;
