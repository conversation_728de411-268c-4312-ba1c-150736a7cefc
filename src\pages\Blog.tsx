import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Container,
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Chip,
  Avatar,
  useTheme,
  useMediaQuery,
  keyframes,
  TextField,
  InputAdornment,
  Button,
  Divider,
  Stack,
  Paper,
  Breadcrumbs,
  Link as MuiLink,
  Fade,
  Slide,
  IconButton,
  Skeleton
} from '@mui/material';
import {
  Search as SearchIcon,
  AccessTime as TimeIcon,
  Share as ShareIcon,
  Bookmark as BookmarkIcon,
  ArrowBack as ArrowBackIcon,
  CalendarToday as CalendarIcon,
  Visibility as ViewIcon,
  ThumbUp as LikeIcon,
  GridView as GridViewIcon,
  ViewList as ListViewIcon,
  Star as StarIcon
} from '@mui/icons-material';
import { Link, useParams, useNavigate } from 'react-router-dom';
import { useLanguage } from '../contexts/LanguageContext';
import { useBlogContent, BlogPost } from '../hooks/useCMSContent';

// Animation keyframes
const fadeInUp = keyframes`
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
`;

const slideInLeft = keyframes`
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
`;



// Blog Detail Component
const BlogDetail: React.FC<{ blogId: string }> = ({ blogId }) => {
  const theme = useTheme();
  const { language } = useLanguage();
  const navigate = useNavigate();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { blogContent, loading } = useBlogContent();
  
  const blog = blogContent?.posts?.find((post: BlogPost) => post.id.toString() === blogId);

  const getLocalizedText = (textObj: any) => {
    if (typeof textObj === 'string') return textObj;
    return textObj?.[language as keyof typeof textObj] || textObj?.en || '';
  };

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Skeleton variant="rectangular" height={400} sx={{ mb: 3, borderRadius: 2 }} />
        <Skeleton variant="text" height={60} sx={{ mb: 2 }} />
        <Skeleton variant="text" height={30} sx={{ mb: 3 }} />
        <Skeleton variant="rectangular" height={200} />
      </Container>
    );
  }

  if (!blog) {
    return (
      <Container maxWidth="lg" sx={{ py: 8, textAlign: 'center' }}>
        <Typography variant="h4" gutterBottom>Blog not found</Typography>
        <Button variant="contained" onClick={() => navigate('/blog')} startIcon={<ArrowBackIcon />}>
          Back to Blog
        </Button>
      </Container>
    );
  }

  return (
    <Box sx={{ minHeight: '100vh', bgcolor: 'background.default' }}>
      {/* Hero Section */}
      <Box
        sx={{
          position: 'relative',
          height: { xs: 300, md: 500 },
          backgroundImage: `url(${blog.image})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          display: 'flex',
          alignItems: 'flex-end',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'linear-gradient(to top, rgba(0,0,0,0.8), rgba(0,0,0,0.3))',
            zIndex: 1
          }
        }}
      >
        <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2, pb: 4 }}>
          <Breadcrumbs sx={{ color: 'white', mb: 2 }}>
            <MuiLink component={Link} to="/" color="inherit">Home</MuiLink>
            <MuiLink component={Link} to="/blog" color="inherit">Blog</MuiLink>
            <Typography color="inherit">{getLocalizedText(blog.title)}</Typography>
          </Breadcrumbs>
          
          <Typography
            variant={isMobile ? 'h4' : 'h2'}
            sx={{
              color: 'white',
              fontWeight: 700,
              mb: 2,
              textShadow: '0 2px 4px rgba(0,0,0,0.5)'
            }}
          >
            {getLocalizedText(blog.title)}
          </Typography>
          
          <Stack direction="row" spacing={2} alignItems="center" sx={{ color: 'white' }}>
            <Avatar sx={{ width: 40, height: 40 }}>
              {getLocalizedText(blog.author).charAt(0)}
            </Avatar>
            <Box>
              <Typography variant="body1" sx={{ fontWeight: 600 }}>
                {getLocalizedText(blog.author)}
              </Typography>
              <Stack direction="row" spacing={2} alignItems="center">
                <Stack direction="row" spacing={0.5} alignItems="center">
                  <CalendarIcon fontSize="small" />
                  <Typography variant="body2">
                    {new Date(blog.date).toLocaleDateString()}
                  </Typography>
                </Stack>
                <Stack direction="row" spacing={0.5} alignItems="center">
                  <TimeIcon fontSize="small" />
                  <Typography variant="body2">
                    {getLocalizedText(blog.readTime)}
                  </Typography>
                </Stack>
              </Stack>
            </Box>
          </Stack>
        </Container>
      </Box>

      {/* Content Section */}
      <Container maxWidth="lg" sx={{ py: 6 }}>
        <Grid container spacing={4}>
          <Grid item xs={12} md={8}>
            <Paper elevation={0} sx={{ p: 4, borderRadius: 3 }}>
              {/* Article Actions */}
              <Stack direction="row" spacing={2} sx={{ mb: 4 }}>
                <Button variant="outlined" startIcon={<ArrowBackIcon />} onClick={() => navigate('/blog')}>
                  Back to Blog
                </Button>
                <Box sx={{ flexGrow: 1 }} />
                <IconButton><ShareIcon /></IconButton>
                <IconButton><BookmarkIcon /></IconButton>
                <IconButton><LikeIcon /></IconButton>
              </Stack>

              {/* Article Content */}
              <Typography
                variant="h5"
                sx={{ mb: 3, color: 'text.secondary', lineHeight: 1.6 }}
              >
                {getLocalizedText(blog.excerpt)}
              </Typography>

              <Divider sx={{ my: 3 }} />

              <Typography
                variant="body1"
                sx={{
                  lineHeight: 1.8,
                  fontSize: '1.1rem',
                  '& p': { mb: 2 },
                  '& h1, & h2, & h3': { mt: 3, mb: 2, fontWeight: 600 },
                  '& ul, & ol': { pl: 3, mb: 2 },
                  '& blockquote': {
                    borderLeft: '4px solid',
                    borderColor: 'primary.main',
                    pl: 2,
                    py: 1,
                    bgcolor: 'grey.50',
                    fontStyle: 'italic'
                  }
                }}
                dangerouslySetInnerHTML={{ __html: getLocalizedText(blog.content) }}
              />

              {/* Tags */}
              <Box sx={{ mt: 4 }}>
                <Typography variant="h6" gutterBottom>Tags</Typography>
                <Stack direction="row" spacing={1} flexWrap="wrap">
                  {blog.tags?.map((tag: string, index: number) => (
                    <Chip
                      key={index}
                      label={tag}
                      variant="outlined"
                      size="small"
                      sx={{ mb: 1 }}
                    />
                  ))}
                </Stack>
              </Box>
            </Paper>
          </Grid>

          {/* Sidebar */}
          <Grid item xs={12} md={4}>
            <Stack spacing={3}>
              {/* Author Info */}
              <Paper elevation={0} sx={{ p: 3, borderRadius: 3 }}>
                <Typography variant="h6" gutterBottom>About the Author</Typography>
                <Stack direction="row" spacing={2} alignItems="center">
                  <Avatar sx={{ width: 60, height: 60 }}>
                    {getLocalizedText(blog.author).charAt(0)}
                  </Avatar>
                  <Box>
                    <Typography variant="h6">{getLocalizedText(blog.author)}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Agricultural Expert
                    </Typography>
                  </Box>
                </Stack>
              </Paper>

              {/* Related Posts */}
              <Paper elevation={0} sx={{ p: 3, borderRadius: 3 }}>
                <Typography variant="h6" gutterBottom>Related Posts</Typography>
                <Stack spacing={2}>
                  {blogContent?.posts?.slice(0, 3).map((relatedPost: BlogPost, index: number) => (
                    relatedPost.id !== blog.id && (
                      <Card
                        key={index}
                        component={Link}
                        to={`/blog/${relatedPost.id}`}
                        sx={{
                          textDecoration: 'none',
                          transition: 'transform 0.2s',
                          '&:hover': { transform: 'translateY(-2px)' }
                        }}
                      >
                        <CardContent sx={{ p: 2 }}>
                          <Typography variant="subtitle2" gutterBottom>
                            {getLocalizedText(relatedPost.title)}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {new Date(relatedPost.date).toLocaleDateString()}
                          </Typography>
                        </CardContent>
                      </Card>
                    )
                  ))}
                </Stack>
              </Paper>
            </Stack>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
};

// Blog List Component
const BlogList: React.FC = () => {
  const theme = useTheme();
  const { language } = useLanguage();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [animate, setAnimate] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  const { blogContent, loading } = useBlogContent();

  useEffect(() => {
    setAnimate(true);
  }, []);

  const getLocalizedText = (textObj: any) => {
    if (typeof textObj === 'string') return textObj;
    return textObj?.[language as keyof typeof textObj] || textObj?.en || '';
  };

  // Sample blog data (fallback if CMS content is not available)
  const sampleBlogPosts: BlogPost[] = [
    {
      id: 1,
      title: {
        en: 'Sustainable Farming Practices for Modern Agriculture',
        kn: 'ಆಧುನಿಕ ಕೃಷಿಗಾಗಿ ಸುಸ್ಥಿರ ಕೃಷಿ ಅಭ್ಯಾಸಗಳು',
        hi: 'आधुनिक कृषि के लिए स्थायी खेती प्रथाएं'
      },
      excerpt: {
        en: 'Discover innovative sustainable farming techniques that are revolutionizing modern agriculture while preserving our environment.',
        kn: 'ನಮ್ಮ ಪರಿಸರವನ್ನು ಸಂರಕ್ಷಿಸುತ್ತಾ ಆಧುನಿಕ ಕೃಷಿಯನ್ನು ಕ್ರಾಂತಿಕಾರಕಗೊಳಿಸುತ್ತಿರುವ ನವೀನ ಸುಸ್ಥಿರ ಕೃಷಿ ತಂತ್ರಗಳನ್ನು ಅನ್ವೇಷಿಸಿ.',
        hi: 'हमारे पर्यावरण को संरक्षित करते हुए आधुनिक कृषि को क्रांतिकारी बना रही नवीन स्थायी खेती तकनीकों की खोज करें।'
      },
      content: {
        en: 'Sustainable farming is a comprehensive approach to agriculture that focuses on environmental health, economic profitability, and social equity...',
        kn: 'ಸುಸ್ಥಿರ ಕೃಷಿಯು ಪರಿಸರ ಆರೋಗ್ಯ, ಆರ್ಥಿಕ ಲಾಭದಾಯಕತೆ ಮತ್ತು ಸಾಮಾಜಿಕ ಸಮಾನತೆಯ ಮೇಲೆ ಕೇಂದ್ರೀಕರಿಸಿದ ಕೃಷಿಯ ಸಮಗ್ರ ವಿಧಾನವಾಗಿದೆ...',
        hi: 'स्थायी खेती कृषि का एक व्यापक दृष्टिकोण है जो पर्यावरणीय स्वास्थ्य, आर्थिक लाभप्रदता और सामाजिक समानता पर केंद्रित है...'
      },
      category: {
        en: 'Sustainable Farming',
        kn: 'ಸುಸ್ಥಿರ ಕೃಷಿ',
        hi: 'स्थायी खेती'
      },
      author: {
        en: 'Dr. Rajesh Kumar',
        kn: 'ಡಾ. ರಾಜೇಶ್ ಕುಮಾರ್',
        hi: 'डॉ. राजेश कुमार'
      },
      date: '2024-01-15',
      readTime: {
        en: '5 min read',
        kn: '5 ನಿಮಿಷ ಓದು',
        hi: '5 मिनट पढ़ें'
      },
      image: '/darvi-images/field1.png',
      featured: true,
      tags: ['sustainable', 'farming', 'environment'],
      views: 1250,
      likes: 89
    }
  ];

  const blogPosts = blogContent?.posts || sampleBlogPosts;
  const categories = blogContent?.categories || [
    { value: 'all', label: { en: 'All', kn: 'ಎಲ್ಲಾ', hi: 'सभी' } },
    { value: 'sustainable', label: { en: 'Sustainable Farming', kn: 'ಸುಸ್ಥಿರ ಕೃಷಿ', hi: 'स्थायी खेती' } },
    { value: 'technology', label: { en: 'Technology', kn: 'ತಂತ್ರಜ್ಞಾನ', hi: 'तकनीक' } },
    { value: 'organic', label: { en: 'Organic Farming', kn: 'ಸಾವಯವ ಕೃಷಿ', hi: 'जैविक खेती' } }
  ];

  const filteredPosts = blogPosts.filter((post: BlogPost) => {
    const postCategory = getLocalizedText(post.category);
    const matchesCategory = selectedCategory === 'all' ||
      postCategory.toLowerCase().includes(selectedCategory);

    const matchesSearch = searchQuery === '' ||
      getLocalizedText(post.title).toLowerCase().includes(searchQuery.toLowerCase()) ||
      getLocalizedText(post.excerpt).toLowerCase().includes(searchQuery.toLowerCase());

    return matchesCategory && matchesSearch;
  });

  const featuredPost = blogPosts.find((post: BlogPost) => post.featured);

  return (
    <Box sx={{ minHeight: '100vh', bgcolor: 'background.default' }}>
      {/* Hero Section */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #1B4C35 0%, #2E7D32 100%)',
          color: 'white',
          py: { xs: 8, md: 12 },
          position: 'relative',
          overflow: 'hidden',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'url(/darvi-images/field1.png)',
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            opacity: 0.1,
            zIndex: 1
          }
        }}
      >
        <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2 }}>
          <Box sx={{ textAlign: 'center', mb: 6 }}>
            <Typography
              variant={isMobile ? 'h3' : 'h1'}
              sx={{
                fontWeight: 700,
                mb: 2,
                background: 'linear-gradient(45deg, #fff, #e8f5e8)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                textShadow: '0 2px 4px rgba(0,0,0,0.3)',
                animation: animate ? `${fadeInUp} 0.8s ease-out` : 'none'
              }}
            >
              {blogContent?.hero?.title?.[language as keyof typeof blogContent.hero.title] ||
               (language === 'en' ? 'Agricultural Insights' :
                language === 'kn' ? 'ಕೃಷಿ ಒಳನೋಟಗಳು' :
                'कृषि अंतर्दृष्टि')}
            </Typography>
            <Typography
              variant="h5"
              sx={{
                opacity: 0.9,
                maxWidth: 600,
                mx: 'auto',
                lineHeight: 1.6,
                animation: animate ? `${fadeInUp} 1s ease-out` : 'none'
              }}
            >
              {blogContent?.hero?.subtitle?.[language as keyof typeof blogContent.hero.subtitle] ||
               (language === 'en' ? 'Expert knowledge, innovative solutions, and sustainable practices for modern agriculture' :
                language === 'kn' ? 'ಆಧುನಿಕ ಕೃಷಿಗಾಗಿ ತಜ್ಞ ಜ್ಞಾನ, ನವೀನ ಪರಿಹಾರಗಳು ಮತ್ತು ಸುಸ್ಥಿರ ಅಭ್ಯಾಸಗಳು' :
                'आधुनिक कृषि के लिए विशेषज्ञ ज्ञान, नवीन समाधान और स्थायी प्रथाएं')}
            </Typography>
          </Box>
        </Container>
      </Box>

      <Container maxWidth="lg" sx={{ py: 6 }}>
        {/* Search and Filter Section */}
        <Paper
          elevation={0}
          sx={{
            p: 3,
            mb: 4,
            borderRadius: 3,
            border: '1px solid',
            borderColor: 'divider',
            animation: animate ? `${slideInLeft} 0.8s ease-out` : 'none'
          }}
        >
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                placeholder={language === 'en' ? 'Search articles...' :
                           language === 'kn' ? 'ಲೇಖನಗಳನ್ನು ಹುಡುಕಿ...' :
                           'लेख खोजें...'}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                  }
                }}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <Stack direction="row" spacing={1} flexWrap="wrap">
                {categories.map((category: any) => (
                  <Chip
                    key={category.value}
                    label={getLocalizedText(category.label)}
                    onClick={() => setSelectedCategory(category.value)}
                    variant={selectedCategory === category.value ? 'filled' : 'outlined'}
                    color={selectedCategory === category.value ? 'primary' : 'default'}
                    sx={{ mb: 1 }}
                  />
                ))}
              </Stack>
            </Grid>
            <Grid item xs={12} md={2}>
              <Stack direction="row" spacing={1}>
                <IconButton
                  onClick={() => setViewMode('grid')}
                  color={viewMode === 'grid' ? 'primary' : 'default'}
                >
                  <GridViewIcon />
                </IconButton>
                <IconButton
                  onClick={() => setViewMode('list')}
                  color={viewMode === 'list' ? 'primary' : 'default'}
                >
                  <ListViewIcon />
                </IconButton>
              </Stack>
            </Grid>
          </Grid>
        </Paper>

        {/* Featured Post */}
        {featuredPost && (
          <Fade in={animate} timeout={1000}>
            <Paper
              elevation={0}
              sx={{
                mb: 6,
                borderRadius: 3,
                overflow: 'hidden',
                border: '1px solid',
                borderColor: 'divider'
              }}
            >
              <Grid container>
                <Grid item xs={12} md={6}>
                  <CardMedia
                    component="img"
                    height="400"
                    image={featuredPost.image}
                    alt={getLocalizedText(featuredPost.title)}
                    sx={{ objectFit: 'cover' }}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <CardContent sx={{ p: 4, height: '100%', display: 'flex', flexDirection: 'column' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Chip
                        icon={<StarIcon />}
                        label={language === 'en' ? 'Featured' :
                               language === 'kn' ? 'ವೈಶಿಷ್ಟ್ಯ' :
                               'विशेष'}
                        color="primary"
                        size="small"
                      />
                      <Chip
                        label={getLocalizedText(featuredPost.category)}
                        variant="outlined"
                        size="small"
                        sx={{ ml: 1 }}
                      />
                    </Box>

                    <Typography
                      variant="h4"
                      sx={{
                        fontWeight: 700,
                        mb: 2,
                        lineHeight: 1.2,
                        color: 'text.primary'
                      }}
                    >
                      {getLocalizedText(featuredPost.title)}
                    </Typography>

                    <Typography
                      variant="body1"
                      sx={{
                        color: 'text.secondary',
                        mb: 3,
                        lineHeight: 1.6,
                        flexGrow: 1
                      }}
                    >
                      {getLocalizedText(featuredPost.excerpt)}
                    </Typography>

                    <Stack direction="row" spacing={2} alignItems="center" sx={{ mb: 3 }}>
                      <Avatar sx={{ width: 32, height: 32 }}>
                        {getLocalizedText(featuredPost.author).charAt(0)}
                      </Avatar>
                      <Box>
                        <Typography variant="body2" sx={{ fontWeight: 600 }}>
                          {getLocalizedText(featuredPost.author)}
                        </Typography>
                        <Stack direction="row" spacing={2} alignItems="center">
                          <Typography variant="caption" color="text.secondary">
                            {new Date(featuredPost.date).toLocaleDateString()}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {getLocalizedText(featuredPost.readTime)}
                          </Typography>
                        </Stack>
                      </Box>
                    </Stack>

                    <Button
                      component={Link}
                      to={`/blog/${featuredPost.id}`}
                      variant="contained"
                      size="large"
                      sx={{ alignSelf: 'flex-start' }}
                    >
                      {language === 'en' ? 'Read More' :
                       language === 'kn' ? 'ಹೆಚ್ಚು ಓದಿ' :
                       'और पढ़ें'}
                    </Button>
                  </CardContent>
                </Grid>
              </Grid>
            </Paper>
          </Fade>
        )}

        {/* Blog Posts Grid/List */}
        <Grid container spacing={3}>
          {loading ? (
            Array.from({ length: 6 }).map((_, index) => (
              <Grid item xs={12} sm={6} md={viewMode === 'grid' ? 4 : 12} key={index}>
                <Card sx={{ height: viewMode === 'grid' ? 400 : 200 }}>
                  <Skeleton variant="rectangular" height={viewMode === 'grid' ? 200 : 120} />
                  <CardContent>
                    <Skeleton variant="text" height={24} sx={{ mb: 1 }} />
                    <Skeleton variant="text" height={20} sx={{ mb: 1 }} />
                    <Skeleton variant="text" height={16} />
                  </CardContent>
                </Card>
              </Grid>
            ))
          ) : filteredPosts.length > 0 ? (
            filteredPosts.map((post: BlogPost, index: number) => (
              !post.featured && (
                <Grid item xs={12} sm={6} md={viewMode === 'grid' ? 4 : 12} key={post.id || index}>
                  <Slide in={animate} direction="up" timeout={300 + index * 100}>
                    <Card
                      sx={{
                        height: '100%',
                        transition: 'all 0.3s ease',
                        cursor: 'pointer',
                        border: '1px solid',
                        borderColor: 'divider',
                        borderRadius: 3,
                        '&:hover': {
                          transform: 'translateY(-8px)',
                          boxShadow: '0 12px 40px rgba(0,0,0,0.15)',
                          borderColor: 'primary.main'
                        }
                      }}
                      component={Link}
                      to={`/blog/${post.id}`}
                      style={{ textDecoration: 'none' }}
                    >
                      {viewMode === 'grid' ? (
                        <>
                          <CardMedia
                            component="img"
                            height="200"
                            image={post.image}
                            alt={getLocalizedText(post.title)}
                            sx={{ objectFit: 'cover' }}
                          />
                          <CardContent sx={{ p: 3, flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                              <Chip
                                label={getLocalizedText(post.category)}
                                size="small"
                                variant="outlined"
                                sx={{ mr: 1 }}
                              />
                              <Typography variant="caption" color="text.secondary">
                                {new Date(post.date).toLocaleDateString()}
                              </Typography>
                            </Box>

                            <Typography
                              variant="h6"
                              sx={{
                                fontWeight: 600,
                                mb: 2,
                                lineHeight: 1.3,
                                color: 'text.primary',
                                display: '-webkit-box',
                                WebkitLineClamp: 2,
                                WebkitBoxOrient: 'vertical',
                                overflow: 'hidden',
                                flexGrow: 1
                              }}
                            >
                              {getLocalizedText(post.title)}
                            </Typography>

                            <Typography
                              variant="body2"
                              sx={{
                                color: 'text.secondary',
                                mb: 3,
                                lineHeight: 1.5,
                                display: '-webkit-box',
                                WebkitLineClamp: 3,
                                WebkitBoxOrient: 'vertical',
                                overflow: 'hidden'
                              }}
                            >
                              {getLocalizedText(post.excerpt)}
                            </Typography>

                            <Stack direction="row" spacing={2} alignItems="center" justifyContent="space-between">
                              <Stack direction="row" spacing={1} alignItems="center">
                                <Avatar sx={{ width: 24, height: 24, fontSize: '0.75rem' }}>
                                  {getLocalizedText(post.author).charAt(0)}
                                </Avatar>
                                <Typography variant="caption" color="text.secondary">
                                  {getLocalizedText(post.author)}
                                </Typography>
                              </Stack>
                              <Stack direction="row" spacing={1} alignItems="center">
                                <ViewIcon fontSize="small" color="action" />
                                <Typography variant="caption" color="text.secondary">
                                  {post.views || 0}
                                </Typography>
                                <LikeIcon fontSize="small" color="action" />
                                <Typography variant="caption" color="text.secondary">
                                  {post.likes || 0}
                                </Typography>
                              </Stack>
                            </Stack>
                          </CardContent>
                        </>
                      ) : (
                        <Box sx={{ display: 'flex', height: 200 }}>
                          <CardMedia
                            component="img"
                            sx={{ width: 200, objectFit: 'cover' }}
                            image={post.image}
                            alt={getLocalizedText(post.title)}
                          />
                          <CardContent sx={{ flex: 1, p: 3 }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                              <Chip
                                label={getLocalizedText(post.category)}
                                size="small"
                                variant="outlined"
                                sx={{ mr: 1 }}
                              />
                              <Typography variant="caption" color="text.secondary">
                                {new Date(post.date).toLocaleDateString()}
                              </Typography>
                            </Box>

                            <Typography
                              variant="h6"
                              sx={{
                                fontWeight: 600,
                                mb: 1,
                                lineHeight: 1.3,
                                color: 'text.primary'
                              }}
                            >
                              {getLocalizedText(post.title)}
                            </Typography>

                            <Typography
                              variant="body2"
                              sx={{
                                color: 'text.secondary',
                                mb: 2,
                                lineHeight: 1.5,
                                display: '-webkit-box',
                                WebkitLineClamp: 2,
                                WebkitBoxOrient: 'vertical',
                                overflow: 'hidden'
                              }}
                            >
                              {getLocalizedText(post.excerpt)}
                            </Typography>

                            <Stack direction="row" spacing={2} alignItems="center" justifyContent="space-between">
                              <Stack direction="row" spacing={1} alignItems="center">
                                <Avatar sx={{ width: 20, height: 20, fontSize: '0.7rem' }}>
                                  {getLocalizedText(post.author).charAt(0)}
                                </Avatar>
                                <Typography variant="caption" color="text.secondary">
                                  {getLocalizedText(post.author)}
                                </Typography>
                              </Stack>
                              <Typography variant="caption" color="text.secondary">
                                {getLocalizedText(post.readTime)}
                              </Typography>
                            </Stack>
                          </CardContent>
                        </Box>
                      )}
                    </Card>
                  </Slide>
                </Grid>
              )
            ))
          ) : (
            <Grid item xs={12}>
              <Paper
                elevation={0}
                sx={{
                  p: 6,
                  textAlign: 'center',
                  borderRadius: 3,
                  border: '1px solid',
                  borderColor: 'divider'
                }}
              >
                <Typography variant="h6" color="text.secondary" gutterBottom>
                  {language === 'en' ? 'No articles found' :
                   language === 'kn' ? 'ಯಾವುದೇ ಲೇಖನಗಳು ಕಂಡುಬಂದಿಲ್ಲ' :
                   'कोई लेख नहीं मिला'}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {language === 'en' ? 'Try adjusting your search or filter criteria' :
                   language === 'kn' ? 'ನಿಮ್ಮ ಹುಡುಕಾಟ ಅಥವಾ ಫಿಲ್ಟರ್ ಮಾನದಂಡಗಳನ್ನು ಸರಿಹೊಂದಿಸಲು ಪ್ರಯತ್ನಿಸಿ' :
                   'अपने खोज या फ़िल्टर मानदंड को समायोजित करने का प्रयास करें'}
                </Typography>
              </Paper>
            </Grid>
          )}
        </Grid>
      </Container>
    </Box>
  );
};

// Main Blog Component
const Blog: React.FC = () => {
  const { blogId } = useParams<{ blogId: string }>();

  return blogId ? <BlogDetail blogId={blogId} /> : <BlogList />;
};

export default Blog;
