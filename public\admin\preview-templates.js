// Preview templates for Netlify CMS
// This file registers custom preview templates for content editing

// Import React and ReactDOM for rendering
const { createElement: h } = React;

// Hero Section Preview
const HeroPreview = ({ entry, widgetFor }) => {
  const slides = entry.getIn(['data', 'slides']);
  
  return h('div', { style: { padding: '16px' } }, [
    h('h3', { key: 'title' }, 'Hero Section Preview'),
    slides && slides.map((slide, index) => 
      h('div', { 
        key: index, 
        style: { 
          border: '1px solid #ddd', 
          padding: '16px', 
          marginBottom: '16px',
          borderRadius: '8px'
        } 
      }, [
        h('h4', { key: 'title' }, slide.getIn(['title', 'en']) || 'Title'),
        h('p', { key: 'desc' }, slide.getIn(['description', 'en']) || 'Description'),
        slide.get('image') && h('img', {
          key: 'img',
          src: slide.get('image'),
          alt: 'Hero',
          style: { maxWidth: '200px', height: 'auto', marginTop: '8px' }
        })
      ])
    )
  ]);
};

// Team Section Preview
const TeamPreview = ({ entry, widgetFor }) => {
  const teamMembers = entry.getIn(['data', 'teamMembers']);
  const sectionTitle = entry.getIn(['data', 'sectionTitle', 'en']) || 'Our Team';
  const sectionSubtitle = entry.getIn(['data', 'sectionSubtitle', 'en']) || 'Meet our team';
  
  return h('div', { style: { padding: '16px' } }, [
    h('h3', { key: 'title' }, 'Team Section Preview'),
    h('h4', { key: 'section-title' }, sectionTitle),
    h('p', { key: 'section-subtitle', style: { color: '#666' } }, sectionSubtitle),
    h('div', { 
      key: 'members',
      style: { display: 'flex', flexWrap: 'wrap', gap: '16px', marginTop: '16px' }
    }, 
      teamMembers && teamMembers.map((member, index) =>
        h('div', {
          key: index,
          style: {
            border: '1px solid #ddd',
            padding: '16px',
            borderRadius: '8px',
            width: '250px'
          }
        }, [
          h('div', { key: 'header', style: { display: 'flex', alignItems: 'center', marginBottom: '8px' } }, [
            member.get('image') && h('img', {
              key: 'avatar',
              src: member.get('image'),
              alt: member.get('name'),
              style: { width: '40px', height: '40px', borderRadius: '50%', marginRight: '8px' }
            }),
            h('div', { key: 'info' }, [
              h('h5', { key: 'name', style: { margin: '0' } }, member.get('name') || 'Name'),
              h('p', { key: 'position', style: { margin: '0', fontSize: '14px', color: '#666' } }, member.get('position') || 'Position')
            ])
          ]),
          h('p', { key: 'desc', style: { fontSize: '14px' } }, member.get('shortDescription') || 'Description'),
          h('span', { 
            key: 'education',
            style: { 
              backgroundColor: '#e3f2fd', 
              padding: '4px 8px', 
              borderRadius: '4px', 
              fontSize: '12px' 
            } 
          }, member.get('education') || 'Education')
        ])
      )
    )
  ]);
};

// Values Section Preview
const ValuesPreview = ({ entry, widgetFor }) => {
  const values = entry.getIn(['data', 'values']);
  
  return h('div', { style: { padding: '16px' } }, [
    h('h3', { key: 'title' }, 'Values Section Preview'),
    h('div', { 
      key: 'values',
      style: { display: 'flex', flexWrap: 'wrap', gap: '16px', marginTop: '16px' }
    }, 
      values && values.map((value, index) =>
        h('div', {
          key: index,
          style: {
            border: '1px solid #ddd',
            padding: '16px',
            borderRadius: '8px',
            width: '250px'
          }
        }, [
          h('h5', { key: 'title', style: { marginBottom: '8px' } }, value.getIn(['title', 'en']) || 'Value Title'),
          h('p', { key: 'desc', style: { fontSize: '14px' } }, value.getIn(['description', 'en']) || 'Value description'),
          h('span', { 
            key: 'icon',
            style: { 
              backgroundColor: '#e8f5e8', 
              padding: '4px 8px', 
              borderRadius: '4px', 
              fontSize: '12px' 
            } 
          }, value.get('icon') || 'Icon')
        ])
      )
    )
  ]);
};

// IoT Features Preview
const IoTFeaturesPreview = ({ entry, widgetFor }) => {
  const features = entry.getIn(['data', 'features']);
  const title = entry.getIn(['data', 'title', 'en']) || 'IoT Features';
  const subtitle = entry.getIn(['data', 'subtitle', 'en']) || 'Subtitle';
  
  return h('div', { style: { padding: '16px' } }, [
    h('h3', { key: 'title' }, 'IoT Features Preview'),
    h('h4', { key: 'section-title' }, title),
    h('p', { key: 'section-subtitle', style: { color: '#666' } }, subtitle),
    h('div', { 
      key: 'features',
      style: { display: 'flex', flexWrap: 'wrap', gap: '16px', marginTop: '16px' }
    }, 
      features && features.map((feature, index) =>
        h('div', {
          key: index,
          style: {
            border: '1px solid #ddd',
            padding: '16px',
            borderRadius: '8px',
            width: '250px'
          }
        }, [
          h('h5', { key: 'title', style: { marginBottom: '8px' } }, feature.getIn(['title', 'en']) || 'Feature Title'),
          h('p', { key: 'desc', style: { fontSize: '14px' } }, feature.getIn(['description', 'en']) || 'Feature description'),
          h('span', { 
            key: 'icon',
            style: { 
              backgroundColor: '#e3f2fd', 
              padding: '4px 8px', 
              borderRadius: '4px', 
              fontSize: '12px' 
            } 
          }, feature.get('icon') || 'Icon')
        ])
      )
    )
  ]);
};

// Contact Section Preview
const ContactPreview = ({ entry, widgetFor }) => {
  const contactInfo = entry.getIn(['data', 'contactInfo']);
  const title = entry.getIn(['data', 'title', 'en']) || 'Contact Us';
  const subtitle = entry.getIn(['data', 'subtitle', 'en']) || 'Get in touch';
  
  return h('div', { style: { padding: '16px' } }, [
    h('h3', { key: 'title' }, 'Contact Section Preview'),
    h('h4', { key: 'section-title' }, title),
    h('p', { key: 'section-subtitle', style: { color: '#666' } }, subtitle),
    h('div', { 
      key: 'contact-info',
      style: { display: 'flex', flexWrap: 'wrap', gap: '16px', marginTop: '16px' }
    }, 
      contactInfo && contactInfo.map((info, index) =>
        h('div', {
          key: index,
          style: {
            border: '1px solid #ddd',
            padding: '16px',
            borderRadius: '8px',
            width: '250px'
          }
        }, [
          h('h5', { key: 'title', style: { marginBottom: '8px' } }, info.getIn(['title', 'en']) || 'Contact Title'),
          h('p', { key: 'content', style: { fontSize: '14px' } }, info.get('content') || 'Contact information'),
          h('span', { 
            key: 'icon',
            style: { 
              backgroundColor: '#fff3e0', 
              padding: '4px 8px', 
              borderRadius: '4px', 
              fontSize: '12px' 
            } 
          }, info.get('icon') || 'Icon')
        ])
      )
    )
  ]);
};

// Gallery Preview
const GalleryPreview = ({ entry, widgetFor }) => {
  const images = entry.getIn(['data', 'images']);
  const title = entry.getIn(['data', 'title', 'en']) || 'Gallery';
  const subtitle = entry.getIn(['data', 'subtitle', 'en']) || 'Our work';
  
  return h('div', { style: { padding: '16px' } }, [
    h('h3', { key: 'title' }, 'Gallery Preview'),
    h('h4', { key: 'section-title' }, title),
    h('p', { key: 'section-subtitle', style: { color: '#666' } }, subtitle),
    h('div', { 
      key: 'images',
      style: { display: 'flex', flexWrap: 'wrap', gap: '16px', marginTop: '16px' }
    }, 
      images && images.map((image, index) =>
        h('div', {
          key: index,
          style: {
            border: '1px solid #ddd',
            padding: '16px',
            borderRadius: '8px',
            width: '200px'
          }
        }, [
          image.get('src') && h('img', {
            key: 'img',
            src: image.get('src'),
            alt: image.get('title') || 'Gallery image',
            style: { width: '100%', height: 'auto', marginBottom: '8px' }
          }),
          h('p', { key: 'title', style: { fontSize: '14px', margin: '0' } }, image.get('title') || 'Image title')
        ])
      )
    )
  ]);
};

// Register preview templates when CMS loads
CMS.registerPreviewTemplate('hero', HeroPreview);
CMS.registerPreviewTemplate('team', TeamPreview);
CMS.registerPreviewTemplate('values', ValuesPreview);
CMS.registerPreviewTemplate('iot-features', IoTFeaturesPreview);
CMS.registerPreviewTemplate('contact', ContactPreview);
CMS.registerPreviewTemplate('gallery', GalleryPreview);
