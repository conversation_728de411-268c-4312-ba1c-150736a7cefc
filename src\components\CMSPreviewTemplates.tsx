import React from 'react';
import { Box, Typography, Card, CardContent, Avatar, Chip } from '@mui/material';

// Preview template for Hero Section
export const HeroPreview: React.FC<{ entry: any; widgetFor: any }> = ({ entry, widgetFor }) => {
  const slides = entry.getIn(['data', 'slides']);
  
  return (
    <Box sx={{ p: 2 }}>
      <Typography variant="h5" gutterBottom>Hero Section Preview</Typography>
      {slides && slides.map((slide: any, index: number) => (
        <Card key={index} sx={{ mb: 2 }}>
          <CardContent>
            <Typography variant="h6">
              {slide.getIn(['title', 'en']) || 'Title'}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {slide.getIn(['description', 'en']) || 'Description'}
            </Typography>
            {slide.get('image') && (
              <Box sx={{ mt: 1 }}>
                <img 
                  src={slide.get('image')} 
                  alt="Hero" 
                  style={{ maxWidth: '200px', height: 'auto' }}
                />
              </Box>
            )}
          </CardContent>
        </Card>
      ))}
    </Box>
  );
};

// Preview template for Team Section
export const TeamPreview: React.FC<{ entry: any; widgetFor: any }> = ({ entry, widgetFor }) => {
  const teamMembers = entry.getIn(['data', 'teamMembers']);
  
  return (
    <Box sx={{ p: 2 }}>
      <Typography variant="h5" gutterBottom>Team Section Preview</Typography>
      <Typography variant="h6" sx={{ mb: 2 }}>
        {entry.getIn(['data', 'sectionTitle', 'en']) || 'Our Team'}
      </Typography>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        {entry.getIn(['data', 'sectionSubtitle', 'en']) || 'Meet our team'}
      </Typography>
      
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
        {teamMembers && teamMembers.map((member: any, index: number) => (
          <Card key={index} sx={{ width: 250 }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar 
                  src={member.get('image')} 
                  sx={{ width: 56, height: 56, mr: 2 }}
                />
                <Box>
                  <Typography variant="h6">
                    {member.get('name') || 'Name'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {member.get('position') || 'Position'}
                  </Typography>
                </Box>
              </Box>
              <Typography variant="body2">
                {member.get('shortDescription') || 'Description'}
              </Typography>
              <Chip 
                label={member.get('education') || 'Education'} 
                size="small" 
                sx={{ mt: 1 }}
              />
            </CardContent>
          </Card>
        ))}
      </Box>
    </Box>
  );
};

// Preview template for Values Section
export const ValuesPreview: React.FC<{ entry: any; widgetFor: any }> = ({ entry, widgetFor }) => {
  const values = entry.getIn(['data', 'values']);
  
  return (
    <Box sx={{ p: 2 }}>
      <Typography variant="h5" gutterBottom>Values Section Preview</Typography>
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
        {values && values.map((value: any, index: number) => (
          <Card key={index} sx={{ width: 250 }}>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 1 }}>
                {value.getIn(['title', 'en']) || 'Value Title'}
              </Typography>
              <Typography variant="body2">
                {value.getIn(['description', 'en']) || 'Value description'}
              </Typography>
              <Chip 
                label={value.get('icon') || 'Icon'} 
                size="small" 
                sx={{ mt: 1 }}
              />
            </CardContent>
          </Card>
        ))}
      </Box>
    </Box>
  );
};

// Preview template for IoT Features
export const IoTFeaturesPreview: React.FC<{ entry: any; widgetFor: any }> = ({ entry, widgetFor }) => {
  const features = entry.getIn(['data', 'features']);
  
  return (
    <Box sx={{ p: 2 }}>
      <Typography variant="h5" gutterBottom>IoT Features Preview</Typography>
      <Typography variant="h6" sx={{ mb: 1 }}>
        {entry.getIn(['data', 'title', 'en']) || 'IoT Features'}
      </Typography>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        {entry.getIn(['data', 'subtitle', 'en']) || 'Subtitle'}
      </Typography>
      
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
        {features && features.map((feature: any, index: number) => (
          <Card key={index} sx={{ width: 250 }}>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 1 }}>
                {feature.getIn(['title', 'en']) || 'Feature Title'}
              </Typography>
              <Typography variant="body2">
                {feature.getIn(['description', 'en']) || 'Feature description'}
              </Typography>
              <Chip 
                label={feature.get('icon') || 'Icon'} 
                size="small" 
                sx={{ mt: 1 }}
              />
            </CardContent>
          </Card>
        ))}
      </Box>
    </Box>
  );
};

// Preview template for Contact Section
export const ContactPreview: React.FC<{ entry: any; widgetFor: any }> = ({ entry, widgetFor }) => {
  const contactInfo = entry.getIn(['data', 'contactInfo']);
  
  return (
    <Box sx={{ p: 2 }}>
      <Typography variant="h5" gutterBottom>Contact Section Preview</Typography>
      <Typography variant="h6" sx={{ mb: 1 }}>
        {entry.getIn(['data', 'title', 'en']) || 'Contact Us'}
      </Typography>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        {entry.getIn(['data', 'subtitle', 'en']) || 'Get in touch'}
      </Typography>
      
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
        {contactInfo && contactInfo.map((info: any, index: number) => (
          <Card key={index} sx={{ width: 250 }}>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 1 }}>
                {info.getIn(['title', 'en']) || 'Contact Title'}
              </Typography>
              <Typography variant="body2">
                {info.get('content') || 'Contact information'}
              </Typography>
              <Chip 
                label={info.get('icon') || 'Icon'} 
                size="small" 
                sx={{ mt: 1 }}
              />
            </CardContent>
          </Card>
        ))}
      </Box>
    </Box>
  );
};

// Preview template for Gallery
export const GalleryPreview: React.FC<{ entry: any; widgetFor: any }> = ({ entry, widgetFor }) => {
  const images = entry.getIn(['data', 'images']);
  
  return (
    <Box sx={{ p: 2 }}>
      <Typography variant="h5" gutterBottom>Gallery Preview</Typography>
      <Typography variant="h6" sx={{ mb: 1 }}>
        {entry.getIn(['data', 'title', 'en']) || 'Gallery'}
      </Typography>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        {entry.getIn(['data', 'subtitle', 'en']) || 'Our work'}
      </Typography>
      
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
        {images && images.map((image: any, index: number) => (
          <Card key={index} sx={{ width: 200 }}>
            <CardContent>
              {image.get('src') && (
                <img 
                  src={image.get('src')} 
                  alt={image.get('title') || 'Gallery image'} 
                  style={{ width: '100%', height: 'auto', marginBottom: 8 }}
                />
              )}
              <Typography variant="body2">
                {image.get('title') || 'Image title'}
              </Typography>
            </CardContent>
          </Card>
        ))}
      </Box>
    </Box>
  );
};

// Preview template for Testimonials
export const TestimonialsPreview: React.FC<{ entry: any; widgetFor: any }> = ({ entry, widgetFor }) => {
  const testimonials = entry.getIn(['data', 'testimonials']);
  
  return (
    <Box sx={{ p: 2 }}>
      <Typography variant="h5" gutterBottom>Testimonials Preview</Typography>
      <Typography variant="h6" sx={{ mb: 1 }}>
        {entry.getIn(['data', 'sectionTitle']) || 'Testimonials'}
      </Typography>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        {entry.getIn(['data', 'sectionSubtitle']) || 'What our clients say'}
      </Typography>
      
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
        {testimonials && testimonials.map((testimonial: any, index: number) => (
          <Card key={index} sx={{ width: 300 }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar 
                  src={testimonial.get('image')} 
                  sx={{ width: 40, height: 40, mr: 2 }}
                />
                <Box>
                  <Typography variant="subtitle2">
                    {testimonial.get('name') || 'Name'}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {testimonial.get('role') || 'Role'}
                  </Typography>
                </Box>
              </Box>
              <Typography variant="body2" sx={{ mb: 1 }}>
                "{testimonial.get('testimonial') || 'Testimonial text'}"
              </Typography>
              <Typography variant="caption">
                Rating: {testimonial.get('rating') || 5}/5
              </Typography>
            </CardContent>
          </Card>
        ))}
      </Box>
    </Box>
  );
};

export default {
  HeroPreview,
  TeamPreview,
  ValuesPreview,
  IoTFeaturesPreview,
  ContactPreview,
  GalleryPreview,
  TestimonialsPreview
};
