// Content Registry - Static imports to avoid webpack warnings
// This file provides a centralized way to import all content files

// Home page content
import heroContent from '../content/home/<USER>';
import valuesContent from '../content/home/<USER>';
import teamContent from '../content/home/<USER>';
import iotFeaturesContent from '../content/home/<USER>';
import contactContent from '../content/home/<USER>';
import galleryContent from '../content/home/<USER>';
import testimonialsContent from '../content/home/<USER>';
import homeAboutContent from '../content/home/<USER>';

// Page content
import aboutContent from '../content/about/content.json';
import blogContent from '../content/blog/content.json';
import checkoutContent from '../content/checkout/content.json';
import contactPageContent from '../content/contact/content.json';
import faqContent from '../content/faq/content.json';
import formContent from '../content/form/content.json';
import iotContent from '../content/iot/content.json';
import pricingContent from '../content/pricing/content.json';
import researchContent from '../content/research/content.json';
import servicesContent from '../content/services/content.json';

// Layout content
import headerContent from '../content/layout/header.json';
import footerContent from '../content/layout/footer.json';

// Legal content
import termsContent from '../content/legal/terms.json';
import privacyContent from '../content/legal/privacy.json';
import refundContent from '../content/legal/refund.json';

// SEO content
import seoContent from '../content/seo/meta.json';

// UI content
import buttonsContent from '../content/ui/buttons.json';
import messagesContent from '../content/ui/messages.json';
import labelsContent from '../content/ui/labels.json';

// Config content
import cmsSettings from '../config/cms-settings.json';
import paymentSettings from '../config/payment-settings.json';
import receiptSettings from '../config/receipt-settings.json';
import featuresConfig from '../config/features.json';

// Content registry object
export const contentRegistry = {
  // Home page sections
  'home/hero': heroContent,
  'home/values': valuesContent,
  'home/team': teamContent,
  'home/iot-features': iotFeaturesContent,
  'home/contact': contactContent,
  'home/gallery': galleryContent,
  'home/testimonials': testimonialsContent,
  'home/about': homeAboutContent,

  // Page content
  'about/content': aboutContent,
  'blog/content': blogContent,
  'checkout/content': checkoutContent,
  'contact/content': contactPageContent,
  'faq/content': faqContent,
  'form/content': formContent,
  'iot/content': iotContent,
  'pricing/content': pricingContent,
  'research/content': researchContent,
  'services/content': servicesContent,

  // Layout content
  'layout/header': headerContent,
  'layout/footer': footerContent,

  // Legal content
  'legal/terms': termsContent,
  'legal/privacy': privacyContent,
  'legal/refund': refundContent,

  // SEO content
  'seo/meta': seoContent,

  // UI content
  'ui/buttons': buttonsContent,
  'ui/messages': messagesContent,
  'ui/labels': labelsContent,

  // Config content
  'config/cms-settings': cmsSettings,
  'config/payment-settings': paymentSettings,
  'config/receipt-settings': receiptSettings,
  'config/features': featuresConfig,
};

// Helper function to get content by path
export const getContent = (path: string) => {
  return contentRegistry[path as keyof typeof contentRegistry];
};

// Helper function to get home page content
export const getHomeContent = () => {
  return {
    hero: contentRegistry['home/hero'],
    values: contentRegistry['home/values'],
    team: contentRegistry['home/team'],
    iotFeatures: contentRegistry['home/iot-features'],
    contact: contentRegistry['home/contact'],
    gallery: contentRegistry['home/gallery'],
    testimonials: contentRegistry['home/testimonials'],
    about: contentRegistry['home/about'],
  };
};

// Helper function to get page content
export const getPageContent = (page: string) => {
  return contentRegistry[`${page}/content` as keyof typeof contentRegistry];
};

// Helper function to get legal content
export const getLegalContent = () => {
  return {
    terms: contentRegistry['legal/terms'],
    privacy: contentRegistry['legal/privacy'],
    refund: contentRegistry['legal/refund'],
  };
};

// Helper function to get UI content
export const getUIContent = () => {
  return {
    buttons: contentRegistry['ui/buttons'],
    messages: contentRegistry['ui/messages'],
    labels: contentRegistry['ui/labels'],
  };
};

// Helper function to get config content
export const getConfigContent = () => {
  return {
    cmsSettings: contentRegistry['config/cms-settings'],
    paymentSettings: contentRegistry['config/payment-settings'],
    receiptSettings: contentRegistry['config/receipt-settings'],
    features: contentRegistry['config/features'],
  };
};

// Helper function to get SEO content
export const getSEOContent = () => {
  return contentRegistry['seo/meta'];
};

// Helper function to get layout content
export const getLayoutContent = () => {
  return {
    header: contentRegistry['layout/header'],
    footer: contentRegistry['layout/footer'],
  };
};

export default contentRegistry;
