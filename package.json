{"name": "darviform", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fontsource/playfair-display": "^5.2.5", "@fontsource/poppins": "^5.2.5", "@mui/icons-material": "^5.17.1", "@mui/material": "^5.17.1", "@types/jspdf": "^1.3.3", "@types/node": "^22.15.21", "@types/react": "^19.1.5", "@types/react-dom": "^19.1.5", "@types/react-slick": "^0.23.13", "@types/uuid": "^10.0.0", "axios": "^1.8.4", "formik": "^2.2.9", "framer-motion": "^12.6.2", "html2canvas": "^1.4.1", "i18next": "^25.0.2", "jspdf": "^3.0.1", "netlify-cms-app": "^2.15.72", "netlify-cms-core": "^2.55.2", "netlify-cms-media-library-cloudinary": "^1.3.10", "netlify-cms-widget-boolean": "^2.4.1", "netlify-cms-widget-datetime": "^2.7.4", "netlify-cms-widget-file": "^2.12.1", "netlify-cms-widget-image": "^2.8.1", "netlify-cms-widget-list": "^2.10.1", "netlify-cms-widget-markdown": "^2.15.1", "netlify-cms-widget-number": "^2.5.0", "netlify-cms-widget-object": "^2.7.2", "netlify-cms-widget-select": "^2.8.2", "netlify-cms-widget-text": "^2.4.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-helmet-async": "^2.0.5", "react-i18next": "^15.5.1", "react-markdown": "^10.1.0", "react-router-dom": "^6.30.0", "react-scripts": "5.0.1", "react-slick": "^0.30.2", "sass": "^1.89.2", "slick-carousel": "^1.8.1", "typescript": "^5.8.3", "uuid": "^11.1.0", "yup": "^1.1.1"}, "devDependencies": {"@types/next": "^8.0.7", "@types/react-router-dom": "^5.3.3", "@typescript-eslint/eslint-plugin": "^5.59.5", "@typescript-eslint/parser": "^5.59.5", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "eslint": "^8.40.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "netlify-cli": "^17.15.7"}, "scripts": {"start": "react-scripts start", "start:netlify": "netlify dev", "start:all": "netlify dev --command \"npm start\" --targetPort 3000", "dev": "netlify dev", "dev:functions": "netlify functions:serve", "build": "react-scripts build", "build:production": "cross-env CI=false SKIP_PREFLIGHT_CHECK=true ESLINT_NO_DEV_ERRORS=true GENERATE_SOURCEMAP=false react-scripts build", "build:windows": "set CI=false&& set SKIP_PREFLIGHT_CHECK=true&& set ESLINT_NO_DEV_ERRORS=true&& set GENERATE_SOURCEMAP=false&& react-scripts build", "build:netlify": "CI=false SKIP_PREFLIGHT_CHECK=true ESLINT_NO_DEV_ERRORS=true GENERATE_SOURCEMAP=false react-scripts build", "build:functions": "cd netlify/functions && npm install", "eject": "react-scripts eject", "lint": "eslint src/**/*.{js,jsx,ts,tsx}", "lint:fix": "eslint --fix src/**/*.{js,jsx,ts,tsx}", "functions:serve": "netlify functions:serve", "deploy": "netlify deploy --prod", "deploy:preview": "netlify deploy"}, "eslintConfig": {"extends": ["react-app"], "rules": {"no-console": "error", "no-unused-vars": "error", "@typescript-eslint/no-unused-vars": "error", "no-debugger": "error"}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}