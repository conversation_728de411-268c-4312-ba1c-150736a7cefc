import { useState, useEffect } from 'react';
import { logError } from '../utils/logger';
import {
  getHomeContent,
  getPageContent,
  getLegalContent,
  getSEOContent,
  getUIContent,
  getConfigContent,
  getContent
} from '../utils/contentRegistry';

// CMS Content Types
export interface CMSSettings {
  site: {
    title: string;
    description: string;
    url: string;
    logo?: string;
    favicon?: string;
  };
  contact: {
    phone: string;
    email: string;
    address: string;
    workingHours: string;
    gstNumber: string;
  };
  social: {
    facebook?: string;
    twitter?: string;
    instagram?: string;
    linkedin?: string;
    youtube?: string;
  };
}

export interface PaymentSettings {
  registration: {
    baseAmount: number;
    gstPercentage: number;
    totalAmount: number;
    currency: string;
    currencySymbol: string;
  };
  gateway: {
    name: string;
    testMode: boolean;
    timeout: number;
  };
  methods: {
    creditCards: boolean;
    debitCards: boolean;
    netBanking: boolean;
    upi: boolean;
    wallets: boolean;
  };
}

export interface ReceiptSettings {
  header: {
    companyName: string;
    title: string;
    gstNumber: string;
    website: string;
    phone: string;
  };
  footer: {
    thankYou: string;
    successMessage: string;
    supportMessage: string;
    tagline: string;
    signatureNote: string;
  };
  styling: {
    primaryColor: string;
    secondaryColor: string;
    fontFamily: string;
    fontSize: number;
  };
}

export interface UIContent {
  buttons: {
    primary: Record<string, string>;
    secondary: Record<string, string>;
    loading: Record<string, string>;
  };
  messages: {
    success: Record<string, string>;
    error: Record<string, string>;
    validation: Record<string, string>;
  };
  labels: {
    fields: Record<string, string>;
    placeholders: Record<string, string>;
  };
}

export interface FeatureFlags {
  payment: {
    enablePayment: boolean;
    enableReceipt: boolean;
    enableEmailNotifications: boolean;
    enableSmsNotifications: boolean;
  };
  form: {
    enableMultiLanguage: boolean;
    enableAutoSave: boolean;
    enableProgressBar: boolean;
    enableValidation: boolean;
  };
  ui: {
    enableDarkMode: boolean;
    enableAnimations: boolean;
    enableLoadingStates: boolean;
    enableTooltips: boolean;
  };
}

// Blog Content Types
export interface BlogPost {
  id: number;
  title: {
    en: string;
    kn: string;
    hi: string;
  };
  excerpt: {
    en: string;
    kn: string;
    hi: string;
  };
  content?: {
    en: string;
    kn: string;
    hi: string;
  };
  category: {
    en: string;
    kn: string;
    hi: string;
  };
  author: {
    en: string;
    kn: string;
    hi: string;
  };
  date: string;
  readTime: {
    en: string;
    kn: string;
    hi: string;
  };
  image: string;
  featured: boolean;
  tags?: string[];
  views?: number;
  likes?: number;
  summary?: {
    en: string;
    kn: string;
    hi: string;
  };
  seoTitle?: {
    en: string;
    kn: string;
    hi: string;
  };
  seoDescription?: {
    en: string;
    kn: string;
    hi: string;
  };
}

export interface BlogContent {
  hero: {
    title: {
      en: string;
      kn: string;
      hi: string;
    };
    subtitle: {
      en: string;
      kn: string;
      hi: string;
    };
    backgroundImage: string;
  };
  posts: BlogPost[];
  categories: Array<{
    value: string;
    label: {
      en: string;
      kn: string;
      hi: string;
    };
  }>;
}

// Custom hook to load CMS content
export const useCMSContent = () => {
  const [cmsSettings, setCMSSettings] = useState<CMSSettings | null>(null);
  const [paymentSettings, setPaymentSettings] = useState<PaymentSettings | null>(null);
  const [receiptSettings, setReceiptSettings] = useState<ReceiptSettings | null>(null);
  const [uiContent, setUIContent] = useState<UIContent | null>(null);
  const [featureFlags, setFeatureFlags] = useState<FeatureFlags | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadCMSContent = () => {
      try {
        setLoading(true);

        // Load all CMS content using static registry
        const configContent = getConfigContent();
        const uiContent = getUIContent();

        setCMSSettings(configContent.cmsSettings);
        setPaymentSettings(configContent.paymentSettings);
        setReceiptSettings(configContent.receiptSettings);
        setUIContent({
          buttons: uiContent.buttons,
          messages: uiContent.messages,
          labels: uiContent.labels
        });
        setFeatureFlags(configContent.features);

        setError(null);
      } catch (err) {
        // Production-safe error logging
        logError('Failed to load CMS content', err);
        setError('Failed to load content');
      } finally {
        setLoading(false);
      }
    };

    loadCMSContent();
  }, []);

  return {
    cmsSettings,
    paymentSettings,
    receiptSettings,
    uiContent,
    featureFlags,
    loading,
    error
  };
};

// Helper hooks for specific content types
export const useUIContent = () => {
  const { uiContent, loading, error } = useCMSContent();
  return { uiContent, loading, error };
};

export const usePaymentSettings = () => {
  const { paymentSettings, loading, error } = useCMSContent();
  return { paymentSettings, loading, error };
};

export const useReceiptSettings = () => {
  const { receiptSettings, loading, error } = useCMSContent();
  return { receiptSettings, loading, error };
};

export const useFeatureFlags = () => {
  const { featureFlags, loading, error } = useCMSContent();
  return { featureFlags, loading, error };
};

export const useSiteSettings = () => {
  const { cmsSettings, loading, error } = useCMSContent();
  return { siteSettings: cmsSettings, loading, error };
};

// Blog content hook
export const useBlogContent = () => {
  const [blogContent, setBlogContent] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadBlogContent = () => {
      try {
        setLoading(true);

        // Load blog content using static registry
        const content = getPageContent('blog');
        setBlogContent(content);
        setError(null);
      } catch (err) {
        // Production-safe error logging
        logError('Failed to load blog content', err);
        setError('Failed to load blog content');
      } finally {
        setLoading(false);
      }
    };

    loadBlogContent();
  }, []);

  return {
    blogContent,
    loading,
    error
  };
};

// Dynamic content loading hook for any content type
export const usePageContent = (contentType: string, fileName?: string) => {
  const [content, setContent] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadContent = () => {
      try {
        setLoading(true);

        let contentPath = '';
        if (fileName) {
          contentPath = `${contentType}/${fileName}`;
        } else {
          contentPath = `${contentType}/content`;
        }

        // Load content using static registry
        const contentData = getContent(contentPath);
        setContent(contentData);
        setError(null);
      } catch (err) {
        // Production-safe error logging
        logError(`Failed to load ${contentType} content`, err);
        setError(`Failed to load ${contentType} content`);
      } finally {
        setLoading(false);
      }
    };

    loadContent();
  }, [contentType, fileName]);

  return {
    content,
    loading,
    error
  };
};

// Specific hooks for different page content
export const useHomeContent = () => {
  const [homeContent, setHomeContent] = useState<any>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadHomeContent = () => {
      try {
        setLoading(true);

        // Load all home page content using static registry
        const content = getHomeContent();
        setHomeContent(content);
        setError(null);
      } catch (err) {
        // Production-safe error logging
        logError('Failed to load home content', err);
        setError('Failed to load home content');
      } finally {
        setLoading(false);
      }
    };

    loadHomeContent();
  }, []);

  return {
    homeContent,
    loading,
    error
  };
};

// SEO content hook
export const useSEOContent = () => {
  const [seoContent, setSEOContent] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadSEOContent = () => {
      try {
        setLoading(true);

        // Load SEO content using static registry
        const content = getSEOContent();
        setSEOContent(content);
        setError(null);
      } catch (err) {
        logError('Failed to load SEO content', err);
        setError('Failed to load SEO content');
      } finally {
        setLoading(false);
      }
    };

    loadSEOContent();
  }, []);

  return {
    seoContent,
    loading,
    error
  };
};

// Legal content hook
export const useLegalContent = () => {
  const [legalContent, setLegalContent] = useState<any>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadLegalContent = () => {
      try {
        setLoading(true);

        // Load legal content using static registry
        const content = getLegalContent();
        setLegalContent(content);
        setError(null);
      } catch (err) {
        logError('Failed to load legal content', err);
        setError('Failed to load legal content');
      } finally {
        setLoading(false);
      }
    };

    loadLegalContent();
  }, []);

  return {
    legalContent,
    loading,
    error
  };
};

// Generic content loader utility - now uses static registry
export const loadContentFile = (path: string) => {
  try {
    return getContent(path);
  } catch (error) {
    logError(`Failed to load content from ${path}`, error);
    throw error;
  }
};

// Content loader for multiple files - now uses static registry
export const loadMultipleContentFiles = (paths: string[]) => {
  try {
    return paths.map(path => getContent(path));
  } catch (error) {
    logError('Failed to load multiple content files', error);
    throw error;
  }
};

// Hook for loading any page content dynamically
export const usePageContentLoader = (contentPaths: Record<string, string>) => {
  const [content, setContent] = useState<Record<string, any>>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadAllContent = () => {
      try {
        setLoading(true);

        const contentEntries = Object.entries(contentPaths);
        const contentObject = contentEntries.reduce((acc, [key, path]) => {
          acc[key] = getContent(path);
          return acc;
        }, {} as Record<string, any>);

        setContent(contentObject);
        setError(null);
      } catch (err) {
        logError('Failed to load page content', err);
        setError('Failed to load page content');
      } finally {
        setLoading(false);
      }
    };

    if (Object.keys(contentPaths).length > 0) {
      loadAllContent();
    }
  }, [contentPaths]);

  return {
    content,
    loading,
    error
  };
};
