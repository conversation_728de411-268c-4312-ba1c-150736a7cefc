<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Darvi Group Content Manager</title>

    <!-- Content Security Policy for Netlify CMS Admin - Permissive for media library -->
    <meta http-equiv="Content-Security-Policy" content="
      default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: https:;
      script-src 'self' 'unsafe-inline' 'unsafe-eval' https: data: blob:;
      style-src 'self' 'unsafe-inline' https: data:;
      font-src 'self' https: data:;
      img-src 'self' data: https: blob:;
      connect-src 'self' https: wss: data:;
      frame-src 'self' https:;
      object-src 'none';
      base-uri 'self';
      form-action 'self' https:;
    " />

    <script src="https://identity.netlify.com/v1/netlify-identity-widget.js"></script>

    <!-- React (required by netlify-cms-app UMD build) -->
    <script src="https://unpkg.com/react@16/umd/react.production.min.js"></script>
    <script src="https://unpkg.com/react-dom@16/umd/react-dom.production.min.js"></script>
  </head>
  <body>
    <!-- Include the script that builds the page and powers Netlify CMS (bundled UMD) -->
    <script src="https://unpkg.com/netlify-cms@2.10.192/dist/netlify-cms.js"></script>
    <script>
      // Initialize Netlify CMS (netlify-cms UMD exposes window.CMS)
      if (window.CMS) {
        window.CMS.init();
      }
    </script>


    <script>
      // Initialize Netlify Identity
      if (window.netlifyIdentity) {
        window.netlifyIdentity.on("init", user => {
          if (!user) {
            window.netlifyIdentity.on("login", () => {
              document.location.href = "/admin/";
            });
          }
        });
      }
    </script>
  </body>
</html>
